package unit

import (
	"context"
	"testing"

	"github.com/charmbracelet/bubbletea"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/tui"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockDBQueries for testing
type MockDBQueries struct {
	mock.Mock
}

func (m *MockDBQueries) ListProfiles(ctx context.Context) ([]db.Profile, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListMCPToolsByProfile(ctx context.Context, profileName string) ([]db.ListMCPToolsByProfileRow, error) {
	args := m.Called(ctx, profileName)
	return args.Get(0).([]db.ListMCPToolsByProfileRow), args.Error(1)
}

func (m *MockDBQueries) ListMCPTools(ctx context.Context) ([]db.McpTool, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.McpTool), args.Error(1)
}

func (m *MockDBQueries) CreateProfileTool(ctx context.Context, arg db.CreateProfileToolParams) (db.ProfileTool, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.ProfileTool), args.Error(1)
}

func (m *MockDBQueries) GetMCPToolByID(ctx context.Context, id int64) (db.McpTool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) DeleteProfileTool(ctx context.Context, arg db.DeleteProfileToolParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

// Add other required methods as no-ops for this test
func (m *MockDBQueries) CreateMCPTool(ctx context.Context, arg db.CreateMCPToolParams) (db.McpTool, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) CreateMCPToolMapping(ctx context.Context, arg db.CreateMCPToolMappingParams) (db.McpToolMapping, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.McpToolMapping), args.Error(1)
}

func (m *MockDBQueries) CreateProfile(ctx context.Context, arg db.CreateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) CreateRole(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) CreateRoleProfile(ctx context.Context, arg db.CreateRoleProfileParams) (db.RoleProfile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.RoleProfile), args.Error(1)
}

func (m *MockDBQueries) CreateUser(ctx context.Context, arg db.CreateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) CreateUserRole(ctx context.Context, arg db.CreateUserRoleParams) (db.UserRole, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.UserRole), args.Error(1)
}

func (m *MockDBQueries) DeleteProfile(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) DeleteRole(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) DeleteRoleProfile(ctx context.Context, arg db.DeleteRoleProfileParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) DeleteUser(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) DeleteUserRole(ctx context.Context, arg db.DeleteUserRoleParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) GetMCPToolByName(ctx context.Context, toolName string) (db.McpTool, error) {
	args := m.Called(ctx, toolName)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) GetProfileByID(ctx context.Context, id int64) (db.Profile, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByName(ctx context.Context, name string) (db.Profile, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByPathSegment(ctx context.Context, pathSegment string) (db.Profile, error) {
	args := m.Called(ctx, pathSegment)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetRoleByID(ctx context.Context, id int64) (db.Role, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) GetRoleByName(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) GetToolWithMapping(ctx context.Context, toolName string) (db.GetToolWithMappingRow, error) {
	args := m.Called(ctx, toolName)
	return args.Get(0).(db.GetToolWithMappingRow), args.Error(1)
}

func (m *MockDBQueries) GetUserByUsername(ctx context.Context, username string) (db.User, error) {
	args := m.Called(ctx, username)
	return args.Get(0).(db.User), args.Error(1)
}



func (m *MockDBQueries) ListUsers(ctx context.Context) ([]db.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.User), args.Error(1)
}

func (m *MockDBQueries) UpdateProfile(ctx context.Context, arg db.UpdateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) UpdateRole(ctx context.Context, arg db.UpdateRoleParams) (db.Role, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) UpdateUser(ctx context.Context, arg db.UpdateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) CreateRoleProfile(ctx context.Context, arg db.CreateRoleProfileParams) (db.RoleProfile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.RoleProfile), args.Error(1)
}

func (m *MockDBQueries) DeleteRoleProfile(ctx context.Context, arg db.DeleteRoleProfileParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) GetRoleByID(ctx context.Context, id int64) (db.Role, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) GetProfileByPathSegment(ctx context.Context, pathSegment string) (db.Profile, error) {
	args := m.Called(ctx, pathSegment)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListRoles(ctx context.Context) ([]db.Role, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) ListProfilesByRole(ctx context.Context, roleID int64) ([]db.Profile, error) {
	args := m.Called(ctx, roleID)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListRolesByUser(ctx context.Context, userID int64) ([]db.Role, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) UpdateRole(ctx context.Context, arg db.UpdateRoleParams) (db.Role, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) DeleteUser(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) DeleteRole(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}





func TestToolAssociation_PressAWithNoTools(t *testing.T) {
	mockQueries := new(MockDBQueries)

	// Setup test data
	profiles := []db.Profile{
		{ID: 1, Name: "MyProfile", Description: pgtype.Text{String: "Test profile", Valid: true}},
	}

	// No tools associated with profile initially
	emptyTools := []db.ListMCPToolsByProfileRow{}

	// Available tools in database
	availableTools := []db.McpTool{
		{ID: 1, ToolName: "TestTool1", Description: pgtype.Text{String: "Test tool 1", Valid: true}},
		{ID: 2, ToolName: "TestTool2", Description: pgtype.Text{String: "Test tool 2", Valid: true}},
	}

	// Set up mock expectations
	mockQueries.On("ListProfiles", mock.Anything).Return(profiles, nil)
	mockQueries.On("ListMCPToolsByProfile", mock.Anything, "MyProfile").Return(emptyTools, nil)
	mockQueries.On("ListMCPTools", mock.Anything).Return(availableTools, nil)

	// Create main TUI model
	m := tui.InitialModelWithQueries(mockQueries).(tui.Model)

	// Navigate to Tool Association
	updatedModel, cmd := m.Update(tea.KeyMsg{Type: tea.KeyDown})
	m = updatedModel.(tui.Model)
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyEnter})
	m = updatedModel.(tui.Model)

	// Execute the command to get the selectMenuItemMsg
	if cmd != nil {
		msg := cmd()
		updatedModel, cmd = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Execute the fetchProfilesCmd to get the initial profiles list
	if cmd != nil {
		msg := cmd() // This should call ListProfiles and return profilesFetchedMsg
		updatedModel, cmd = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Select the profile (press enter)
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyEnter})
	m = updatedModel.(tui.Model)

	// Execute the command to load tools for profile
	if cmd != nil {
		msg := cmd() // This should call ListMCPToolsByProfile and return toolsFetchedMsg
		updatedModel, cmd = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Verify we're in the tool list view with no tools
	view := m.View()
	assert.Contains(t, view, "Tools for Profile 'MyProfile':")
	assert.Contains(t, view, "No tools associated with this profile. Press 'a' to associate one.")

	// Now press 'a' to associate a tool - this should work even with no tools
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'a'}})
	m = updatedModel.(tui.Model)

	// Execute the command to load available tools
	if cmd != nil {
		msg := cmd() // This should call ListMCPTools and return availableToolsFetchedMsg
		updatedModel, _ = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Verify the view shows available tools
	view = m.View()
	assert.Contains(t, view, "Available Tools to Associate with Profile 'MyProfile':")
	assert.Contains(t, view, "TestTool1")
	assert.Contains(t, view, "TestTool2")
	assert.Contains(t, view, "Press 'enter' to select, 'esc' to go back.")

	mockQueries.AssertExpectations(t)
}
